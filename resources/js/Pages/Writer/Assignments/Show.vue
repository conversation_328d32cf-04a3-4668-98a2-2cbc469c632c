<template>
    <div class="space-y-6">

        <div class="sm:flex sm:items-center flex-row gap-4 justify-between items-center">
            <div class="sm:flex-auto mb-6">
                <div class="flex flex-row gap-1 items-center">
                    <Link :href="route('admin.writer.assignment.index')" class="link-indigo-icon">
                    <CircleChevronLeft class="h-6 w-6" />
                    </Link>

                    <h2 class="title-1 ">Assignment #{{ item.id }}</h2>
                </div>
                <p class="subtitle-1">My writing assignments.</p>
            </div>
            <div class="border rounded-md p-6 bg-gray-100 flex flex-row gap-2 items-center text-2xl font-bold">
                <span class="">Status:</span>
                <ContentStatus :status="item.content_status" class="text-2xl " />

            </div>
        </div>

        <div class="grid grid-cols-2 gap-4">
            <div class="border rounded-md flex flex-col gap-4 text-sm">

                <div class="p-4">
                    <h3 class="text-lg font-semibold mb-6">Requirements</h3>
                    <div class="text-sm flex flex-col gap-2">
                        <div class="border-b border-gray-200 pb-2">
                            <span class="font-bold">Article Topic:</span> <span>{{ item.requirements?.article_topic
                            }}</span>
                        </div>
                        <div class="border-b border-gray-200 pb-2">
                            <span class="font-bold">Anchor Text:</span> <span>{{ item.requirements?.anchor_text
                            }}</span>
                        </div>
                        <div class="border-b border-gray-200 pb-2">
                            <span class="font-bold">Advertiser Url:</span> <span>{{ item.requirements?.advertiser_url
                            }}</span>
                        </div>
                        <div class="border-b border-gray-200 pb-2">
                            <span class="font-bold">Requirement Comments:</span> <span>{{
                                item.requirements?.requirement_comments ?? "N/A" }}</span>
                        </div>
                    </div>
                </div>

                <div class="p-4">
                    <h3 class="text-lg font-semibold mb-4">Publisher's Details</h3>

                    <div class="border-b border-gray-200 pb-2 flex flex-row gap-2">
                        <span class="font-bold">Publisher Website:</span> <span>{{ item.website?.website_domain
                        }}</span>
                        <a :href="returnWebsiteUrl(item.website)" target="_blank" class="text-indigo-500">
                            <ExternalLink class="w-4 h-4" />
                        </a>
                    </div>
                    <div class="border-b border-gray-200 pt-2 pb-2 flex flex-row gap-2">
                        <span class="font-bold">Order Status:</span> <span>{{ item.state_label }}</span>
                    </div>
                </div>

                <div v-if="item.order?.user?.advertiser_guidelines?.guidelines_for_writer" class="p-4">
                    <h3 class="text-lg font-semibold mb-4">Advertiser Guidelines</h3>
                    <div class="border-b border-gray-200 pb-2 ">
                        <span class="font-bold"></span> <span>{{
                            item.order?.user?.advertiser_guidelines?.guidelines_for_writer ?? "N/A" }}</span>
                    </div>

                </div>
            </div>
            <div class="border rounded-md p-4">
                <ContentForm :item="item" :media="media" />
            </div>
        </div>
    </div>
</template>

<script setup>
import ContentForm from '@/Pages/Admin/Orders/Details/SingleOrderDetails/ContentForm.vue'
import { returnWebsiteUrl } from '@/lib/utils';
import { ExternalLink, CircleChevronLeft } from 'lucide-vue-next'
import ContentStatus from '@/Components/ContentStatus.vue'
defineProps({
    item: Object,
    media: Array,
})


</script>