<template>
    <div class="space-y-6">
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                Withdrawal Request Details
            </h2>
            <div class="flex space-x-4">
                <button v-if="withdrawal.status === 'pending'" @click="showApprovalModal = true"
                    class="inline-flex items-center px-4 py-2 bg-green-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-green-700">
                    Approve Withdrawal
                </button>
            </div>
        </div>

        <!-- Approval Modal -->
        <div v-if="showApprovalModal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center">
            <div class="bg-white rounded-lg p-6 max-w-md w-full">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Approve Withdrawal</h3>
                <div class="mb-4">
                    <label for="approval-url" class="block text-sm font-medium text-gray-700">Payment URL</label>
                    <input type="text" id="approval-url" v-model="approvalUrl"
                        class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                        placeholder="Enter payment URL" />
                    <p v-if="errors.payment_url" class="mt-1 text-sm text-red-600">{{ errors.payment_url }}</p>
                </div>

                <!-- File Uploader -->
                <div class="w-full mb-4">
                    <label class="block text-sm font-medium text-gray-900 mb-2">Media Files</label>
                    <!-- Drop Zone -->
                    <div class="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center cursor-pointer transition-all duration-200 ease-in-out"
                        :class="{ 'border-blue-500 bg-blue-50 scale-[1.02]': isDragging, 'hover:border-blue-400 hover:bg-gray-50': !isDragging }"
                        @dragover.prevent="isDragging = true" @dragleave.prevent="isDragging = false"
                        @drop.prevent="handleDrop" @click="triggerFileInput">
                        <input type="file" ref="fileInput" class="hidden" multiple
                            accept=".pdf,.doc,.docx,.png,.jpg,.jpeg" @change="handleFileSelect">
                        <div class="space-y-3 text-center">
                            <div class="flex justify-center">
                                <Upload class="h-12 w-12 text-gray-400 transition-transform duration-200"
                                    :class="{ 'scale-110 text-blue-500': isDragging }" />
                            </div>
                            <div class="flex flex-col items-center space-y-1">
                                <span class="text-sm font-medium text-gray-700">Drop files here or click to
                                    upload</span>
                                <p class="text-xs text-gray-500">
                                    PDF, DOC, PNG, JPG up to 10MB
                                </p>
                            </div>
                        </div>
                    </div>

                    <!-- File List -->
                    <div v-if="selectedFiles.length > 0" class="mt-4 space-y-2">
                        <div v-for="(file, index) in selectedFiles" :key="index"
                            class="group flex items-center justify-between bg-white px-4 py-3 rounded-lg border border-gray-200 hover:border-gray-300 transition-all duration-200">
                            <div class="flex items-center gap-3 min-w-0">
                                <div class="flex-shrink-0">
                                    <div class="w-8 h-8 rounded-lg bg-gray-100 flex items-center justify-center">
                                        <span class="text-xs font-medium text-gray-600">
                                            {{ file.name.split('.').pop().toUpperCase() }}
                                        </span>
                                    </div>
                                </div>
                                <div class="min-w-0 flex-1">
                                    <p class="text-sm font-medium text-gray-900 truncate">{{ file.name }}</p>
                                    <p class="text-xs text-gray-500">{{ formatFileSize(file.size) }}</p>
                                </div>
                                <!-- Progress Bar -->
                                <div v-if="file.uploading" class="w-32">
                                    <div class="w-full bg-gray-100 rounded-full h-1.5 overflow-hidden">
                                        <div class="bg-blue-600 h-1.5 rounded-full transition-all duration-300 ease-out"
                                            :style="{ width: file.progress + '%' }"></div>
                                    </div>
                                    <p class="text-xs text-gray-500 mt-1 text-right">{{ file.progress }}%</p>
                                </div>
                            </div>
                            <button v-if="!file.uploading" type="button" @click="removeFile(index)"
                                class="flex-shrink-0 p-1 text-gray-400 hover:text-red-500 rounded-full hover:bg-red-50 transition-colors duration-200">
                                <X class="w-4 h-4" />
                            </button>
                        </div>
                    </div>
                </div>

                <div class="flex justify-end space-x-3">
                    <button @click="showApprovalModal = false"
                        class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50">
                        Cancel
                    </button>
                    <button @click="handleApproval"
                        class="px-4 py-2 text-sm font-medium text-white bg-green-600 border border-transparent rounded-md hover:bg-green-700">
                        Approve
                    </button>
                </div>
            </div>
        </div>

        <div class="py-12">
            <div class="max-w-8xl mx-auto sm:px-6 lg:px-8">
                <div class="bg-white overflow-hidden shadow-xl sm:rounded-lg">
                    <div class="p-6">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <!-- Withdrawal Details -->
                            <div class="bg-gray-50 p-6 rounded-lg">
                                <h3 class="text-lg font-medium text-gray-900 mb-4">Withdrawal Information</h3>
                                <dl class="space-y-4">
                                    <div>
                                        <dt class="text-sm font-medium text-gray-500">Withdrawal ID</dt>
                                        <dd class="mt-1 text-sm text-gray-900">{{ withdrawal.id }}</dd>
                                    </div>
                                    <div>
                                        <dt class="text-sm font-medium text-gray-500">Status</dt>
                                        <dd class="mt-1">
                                            <span :class="[
                                                'px-2 inline-flex text-xs leading-5 font-semibold rounded-full',
                                                {
                                                    'bg-yellow-100 text-yellow-800': withdrawal.status === 'pending',
                                                    'bg-green-100 text-green-800': withdrawal.status === 'approved',
                                                    'bg-red-100 text-red-800': withdrawal.status === 'rejected'
                                                }
                                            ]">
                                                {{ withdrawal.status }}
                                            </span>
                                        </dd>
                                    </div>
                                    <div>
                                        <dt class="text-sm font-medium text-gray-500">Amount</dt>
                                        <dd class="mt-1 text-sm text-gray-900">
                                            ${{ (withdrawal.amount / 100).toFixed(2) }}</dd>
                                    </div>
                                    <div>
                                        <dt class="text-sm font-medium text-gray-500">Requested At</dt>
                                        <dd class="mt-1 text-sm text-gray-900">{{ withdrawal.created_at_formatted }}
                                        </dd>
                                    </div>
                                </dl>
                            </div>

                            <!-- User Information -->
                            <div class="bg-gray-50 p-6 rounded-lg">
                                <h3 class="text-lg font-medium text-gray-900 mb-4">User Information</h3>
                                <dl class="space-y-4">
                                    <div>
                                        <dt class="text-sm font-medium text-gray-500">Name</dt>
                                        <dd class="mt-1 text-sm text-gray-900">{{ user.name || 'N/A' }}</dd>
                                    </div>
                                    <div>
                                        <dt class="text-sm font-medium text-gray-500">Email</dt>
                                        <dd class="mt-1 text-sm text-gray-900">{{ user.email || 'N/A' }}</dd>
                                    </div>
                                </dl>
                            </div>
                        </div>
                        <!-- Payment Details -->
                        <div class="bg-gray-50 p-6 rounded-lg mt-5">
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Payment Details</h3>
                            <dl class="space-y-4">
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Payment Method</dt>
                                    <dd class="mt-1 text-sm text-gray-900">
                                        <span
                                            class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                            {{ paymentMethod?.key?.toUpperCase() || 'Stripe' }}
                                        </span>
                                    </dd>
                                </div>
                                <div v-if="paymentMethod?.value"
                                    class="mt-4 bg-gray-50 rounded-lg p-4 border border-gray-200">
                                    <dt class="text-sm font-medium text-gray-500 mb-3">Payment Details</dt>
                                    <dd class="mt-1 text-sm text-gray-900 space-y-2">
                                        <template v-if="paymentMethod.key === 'paypal'">
                                            <div class="flex items-center space-x-2">
                                                <svg class="h-5 w-5 text-gray-400" fill="currentColor"
                                                    viewBox="0 0 24 24">
                                                    <path
                                                        d="M20.067 8.478c.492.315.844.825.844 1.522 0 1.845-1.534 3.373-3.373 3.373h-.674c-.372 0-.674.302-.674.674v1.348c0 .372-.302.674-.674.674H7.373c-.372 0-.674-.302-.674-.674v-1.348c0-.372-.302-.674-.674-.674h-.674C3.534 13.373 2 11.845 2 10c0-.697.352-1.207.844-1.522C2.352 7.163 2 6.653 2 5.956c0-1.845 1.534-3.373 3.373-3.373h13.254C20.466 2.583 22 4.111 22 5.956c0 .697-.352 1.207-.844 1.522zM5.373 4.583c-1.119 0-2.023.904-2.023 2.023 0 1.119.904 2.023 2.023 2.023h13.254c1.119 0 2.023-.904 2.023-2.023 0-1.119-.904-2.023-2.023-2.023H5.373zm0 8.046c-1.119 0-2.023.904-2.023 2.023 0 1.119.904 2.023 2.023 2.023h13.254c1.119 0 2.023-.904 2.023-2.023 0-1.119-.904-2.023-2.023-2.023H5.373z" />
                                                </svg>
                                                <span>Email: {{ paymentMethod.value.email }}</span>
                                            </div>
                                        </template>
                                        <template v-else-if="paymentMethod.key === 'bank'">
                                            <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
                                                <div class="flex items-center space-x-2">
                                                    <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor"
                                                        viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round"
                                                            stroke-width="2"
                                                            d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z" />
                                                    </svg>
                                                    <span>Bank Name: {{ paymentMethod.value.bank_name }}</span>
                                                </div>
                                                <div class="flex items-center space-x-2">
                                                    <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor"
                                                        viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round"
                                                            stroke-width="2"
                                                            d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                                                    </svg>
                                                    <span>Account Name: {{ paymentMethod.value.account_name }}</span>
                                                </div>
                                                <div class="flex items-center space-x-2">
                                                    <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor"
                                                        viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round"
                                                            stroke-width="2" d="M4 6h16M4 10h16M4 14h16M4 18h16" />
                                                    </svg>
                                                    <span>Account Number: {{ paymentMethod.value.account_number
                                                    }}</span>
                                                </div>
                                                <div v-if="paymentMethod.value.iban"
                                                    class="flex items-center space-x-2">
                                                    <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor"
                                                        viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round"
                                                            stroke-width="2"
                                                            d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z" />
                                                    </svg>
                                                    <span>IBAN: {{ paymentMethod.value.iban }}</span>
                                                </div>
                                                <div v-if="paymentMethod.value.swift_code"
                                                    class="flex items-center space-x-2">
                                                    <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor"
                                                        viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round"
                                                            stroke-width="2"
                                                            d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z" />
                                                    </svg>
                                                    <span>SWIFT Code: {{ paymentMethod.value.swift_code }}</span>
                                                </div>
                                                <div v-if="paymentMethod.value.bank_address"
                                                    class="flex items-center space-x-2">
                                                    <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor"
                                                        viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round"
                                                            stroke-width="2"
                                                            d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                                                        <path stroke-linecap="round" stroke-linejoin="round"
                                                            stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                                                    </svg>
                                                    <span>Bank Address: {{ paymentMethod.value.bank_address }}</span>
                                                </div>
                                                <div v-if="paymentMethod.value.country"
                                                    class="flex items-center space-x-2">
                                                    <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor"
                                                        viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round"
                                                            stroke-width="2"
                                                            d="M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                                    </svg>
                                                    <span>Country: {{ paymentMethod.value.country }}</span>
                                                </div>
                                            </div>
                                        </template>
                                        <template v-else-if="paymentMethod.key === 'payoneer'">
                                            <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
                                                <div class="flex items-center space-x-2">
                                                    <svg class="h-5 w-5 text-gray-400" fill="currentColor"
                                                        viewBox="0 0 24 24">
                                                        <path
                                                            d="M20.067 8.478c.492.315.844.825.844 1.522 0 1.845-1.534 3.373-3.373 3.373h-.674c-.372 0-.674.302-.674.674v1.348c0 .372-.302.674-.674.674H7.373c-.372 0-.674-.302-.674-.674v-1.348c0-.372-.302-.674-.674-.674h-.674C3.534 13.373 2 11.845 2 10c0-.697.352-1.207.844-1.522C2.352 7.163 2 6.653 2 5.956c0-1.845 1.534-3.373 3.373-3.373h13.254C20.466 2.583 22 4.111 22 5.956c0 .697-.352 1.207-.844 1.522zM5.373 4.583c-1.119 0-2.023.904-2.023 2.023 0 1.119.904 2.023 2.023 2.023h13.254c1.119 0 2.023-.904 2.023-2.023 0-1.119-.904-2.023-2.023-2.023H5.373zm0 8.046c-1.119 0-2.023.904-2.023 2.023 0 1.119.904 2.023 2.023 2.023h13.254c1.119 0 2.023-.904 2.023-2.023 0-1.119-.904-2.023-2.023-2.023H5.373z" />
                                                    </svg>
                                                    <span>Email: {{ paymentMethod.value.email }}</span>
                                                </div>
                                                <div v-if="paymentMethod.value.account_id"
                                                    class="flex items-center space-x-2">
                                                    <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor"
                                                        viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round"
                                                            stroke-width="2"
                                                            d="M10 6H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V8a2 2 0 00-2-2h-5m-4 0V5a2 2 0 114 0v1m-4 0a2 2 0 104 0m-5 8a2 2 0 100-4 2 2 0 000 4zm0 0c1.306 0 2.417.835 2.83 2M9 14a3.001 3.001 0 00-2.83 2M15 11h3m-3 4h2" />
                                                    </svg>
                                                    <span>Account ID: {{ paymentMethod.value.account_id }}</span>
                                                </div>
                                            </div>
                                        </template>
                                    </dd>
                                </div>
                                <div v-else>
                                    <div class="mt-4">
                                        <table class="min-w-full divide-y divide-gray-200">
                                            <thead class="bg-gray-50">
                                                <tr>
                                                    <th scope="col"
                                                        class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                        Payment ID
                                                    </th>
                                                    <th scope="col"
                                                        class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                        Stripe PI
                                                    </th>
                                                    <th scope="col"
                                                        class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                        Amount
                                                    </th>
                                                    <th scope="col"
                                                        class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                        Status
                                                    </th>
                                                    <th scope="col"
                                                        class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                        Date
                                                    </th>
                                                </tr>
                                            </thead>
                                            <tbody class="bg-white divide-y divide-gray-200">
                                                <tr v-for="payment in payments" :key="payment.id">
                                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                        {{ payment.id }}
                                                    </td>
                                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                        {{ payment.external_transaction_id }}
                                                    </td>
                                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                        ${{ payment.payment_amount }}
                                                    </td>
                                                    <td class="px-6 py-4 whitespace-nowrap">
                                                        <span :class="[
                                                            'px-2 inline-flex text-xs leading-5 font-semibold rounded-full',
                                                            {
                                                                'bg-green-100 text-green-800': payment.status === 'paid',
                                                                'bg-yellow-100 text-yellow-800': payment.status === 'pending',
                                                                'bg-red-100 text-red-800': payment.status === 'refunded',
                                                                'bg-orange-100 text-orange-800': payment.status === 'partially_refunded',
                                                                'bg-blue-100 text-blue-800': payment.status === 'credit_applied'
                                                            }
                                                        ]">
                                                            {{ payment.status }}
                                                        </span>
                                                    </td>
                                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                        {{ payment.created_at_formatted }}
                                                    </td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>
        </div>


        <div class="py-12" v-if="withdrawal.status === 'approved'">
            <div class="max-w-8xl mx-auto sm:px-6 lg:px-8">
                <div class="bg-white overflow-hidden shadow-xl sm:rounded-lg">
                    <div class="p-6">
                        <div class="grid grid-cols-1 md:grid-cols-1 gap-6">
                            <!-- Withdrawal Details -->
                            <div class="bg-gray-50 p-6 rounded-lg">
                                <h3 class="text-lg font-medium text-gray-900 mb-4">Approved Media Files</h3>
                                <p class="text-gray-500 mb-4">Payment URL: {{ withdrawal.payment_url ?? 'N/A' }}</p>
                                <div v-if="media.length > 0" class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <div v-for="file in media" :key="file.id"
                                        class="bg-white border border-gray-200 rounded-lg p-4 hover:border-emerald-500 transition-colors duration-200">
                                        <div class="flex items-center space-x-4">
                                            <div class="flex-shrink-0">
                                                <template v-if="file.mime_type.includes('image')">
                                                    <img :src="file.url" :alt="file.name"
                                                        class="w-16 h-16 object-cover rounded-lg shadow-sm">
                                                </template>
                                                <template v-else>
                                                    <div
                                                        class="w-16 h-16 flex items-center justify-center bg-gray-100 rounded-lg">
                                                        <svg class="w-8 h-8 text-gray-400" fill="none"
                                                            stroke="currentColor" viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round"
                                                                stroke-width="2"
                                                                d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
                                                        </svg>
                                                    </div>
                                                </template>
                                            </div>
                                            <div class="flex-1 min-w-0">
                                                <p class="text-sm font-medium text-gray-900 truncate mb-1">
                                                    {{ file.name }}
                                                </p>
                                                <div class="flex items-center space-x-2 text-xs text-gray-500">
                                                    <span>{{ formatFileSize(file.size) }}</span>
                                                    <span>•</span>
                                                    <span>{{ getFileExtension(file.name) }}</span>
                                                </div>
                                            </div>
                                            <div class="flex-shrink-0">
                                                <a :href="file.url" target="_blank" rel="noopener noreferrer"
                                                    class="inline-flex items-center px-2.5 py-1.5 text-xs font-medium text-emerald-700 bg-emerald-50 hover:bg-emerald-100 rounded-md transition-colors duration-200">
                                                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor"
                                                        viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round"
                                                            stroke-width="2"
                                                            d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                                                    </svg>
                                                    View
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div v-else>
                                    <p class="text-gray-500">No media files uploaded</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { router } from '@inertiajs/vue3';
import { ref, inject } from 'vue';
import { ArrowUpTrayIcon as Upload, XMarkIcon as X } from '@heroicons/vue/24/outline';

const props = defineProps({
    withdrawal: {
        type: Object,
        required: true
    },
    user: {
        type: Object,
        required: true
    },
    paymentMethod: {
        type: Object,
        required: true
    },
    media: {
        type: Array,
        required: true
    },
    payments: {
        type: Array,
        required: true
    },
    errors: {
        type: Object,
        default: () => ({})
    }
});

const selectedFiles = ref([])
const notify = inject('$notify');
const showApprovalModal = ref(false);
const approvalUrl = ref('');
const isDragging = ref(false);
const fileInput = ref(null);

const isValidUrl = (url) => {
    try {
        new URL(url);
        return true;
    } catch (e) {
        return false;
    }
};

const handleApproval = () => {
    if (!approvalUrl.value && selectedFiles.value.length === 0) {
        notify('Please provide either a payment URL or upload media files', {
            type: 'error'
        });
        return;
    }

    if (approvalUrl.value && !isValidUrl(approvalUrl.value)) {
        notify('Please enter a valid URL', {
            type: 'error'
        });
        return;
    }

    if (confirm('Are you sure you want to approve this withdrawal request?')) {
        router.post(route('admin.withdrawals.approve', props.withdrawal.id), {
            payment_url: approvalUrl.value,
            media_files: selectedFiles.value.map(file => file.id)
        }, {
            onSuccess: () => {
                showApprovalModal.value = false;
                approvalUrl.value = '';
                selectedFiles.value = [];
                notify('Withdrawal request approved successfully', {
                    type: 'success'
                });
            },
            onError: (errors) => {
                notify('Failed to approve withdrawal request', {
                    type: 'error'
                });
            }
        });
    }
};

// File handling
const triggerFileInput = () => {
    fileInput.value.click()
}

const handleFileSelect = (event) => {
    const files = Array.from(event.target.files)
    validateAndAddFiles(files)
}

const handleDrop = (event) => {
    isDragging.value = false
    const files = Array.from(event.dataTransfer.files)
    validateAndAddFiles(files)
}

const validateAndAddFiles = async (files) => {
    const validTypes = ['.pdf', '.doc', '.docx', '.png', '.jpg', '.jpeg']
    const maxSize = 10 * 1024 * 1024 // 10MB

    for (const file of files) {
        const extension = '.' + file.name.split('.').pop().toLowerCase()
        if (!validTypes.includes(extension)) {
            notify(`Invalid file type: ${file.name}. Allowed types: PDF, DOC, PNG, JPG`, { type: 'error' })
            continue
        }
        if (file.size > maxSize) {
            notify(`File too large: ${file.name}. Maximum size is 10MB`, { type: 'error' })
            continue
        }

        // Add file to selected files with uploading state
        const fileIndex = selectedFiles.value.length
        selectedFiles.value.push({
            name: file.name,
            size: file.size,
            uploading: true,
            progress: 0
        })

        try {
            const formData = new FormData()
            formData.append('file', file)

            const response = await axios.post('/upload', formData, {
                headers: {
                    'Content-Type': 'multipart/form-data',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                },
                onUploadProgress: (progressEvent) => {
                    const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total)
                    selectedFiles.value[fileIndex].progress = progress
                }
            })

            if (response.data.success) {
                // Update file data with server response
                selectedFiles.value[fileIndex] = {
                    id: response.data.media_id,
                    name: response.data.name,
                    url: response.data.url,
                    size: response.data.size,
                    uploading: false,
                    progress: 100
                }
                notify('File uploaded successfully', { type: 'success' })
            }
        } catch (error) {
            // Remove failed upload from selected files
            selectedFiles.value.splice(fileIndex, 1)
            notify(`Failed to upload ${file.name}: ${error.response?.data?.message || 'Unknown error'}`, { type: 'error' })
        }
    }
}

const removeFile = (index) => {
    selectedFiles.value.splice(index, 1)
}

const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

const getFileExtension = (filename) => {
    return filename.split('.').pop().toUpperCase();
};
</script>