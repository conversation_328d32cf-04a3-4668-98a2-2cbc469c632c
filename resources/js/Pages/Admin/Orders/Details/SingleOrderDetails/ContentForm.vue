<template>
    <div>


        <!-- Revision Request Alert -->
        <div v-if="item.state_name === orderItemStates.ContentRevisionRequestedByAdvertiser"
            class="mb-6 bg-amber-50 border border-amber-200 rounded-lg">
            <div class="p-4">
                <div class="flex items-start gap-3">
                    <AlertTriangle class="w-5 h-5 text-amber-500 mt-0.5 flex-shrink-0" />
                    <div class="flex flex-col gap-1">
                        <span class="font-medium text-amber-800">Content Revision Required</span>
                        <span v-if="item.content?.publisher_advertiser_revision_reason"
                            class="text-sm text-amber-700">Reason From
                            Publisher: {{
                                item.content?.publisher_advertiser_revision_reason }}</span>
                        <span v-else class="text-sm text-amber-700">Reason: {{ item.content?.advertiser_revision_reason
                            }}</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Header with Edit button -->
        <div class="flex justify-between mb-5">
            <div class="flex flex-row gap-2 items-center">
                <h3 class="text-base/7 font-semibold text-gray-900">Content Details</h3>
                <div v-if="item.content?.content_url || item.content?.content_body">
                    <Check class="w-4 h-4 text-green-500" />
                </div>
            </div>
            <button class="text-blue-500 text-sm px-3 py-1 rounded-md flex flex-row gap-1 items-center"
                :class="{ 'opacity-50 cursor-not-allowed': !canEditContent }" @click="startEdit" v-if="!showEdit"
                :disabled="!canEditContent">
                <Pencil class="w-4 h-4" />
                <span>Edit Content</span>
            </button>

        </div>
        <!-- <div class="text-xs text-gray-500">
            <div>{{ item.state_name }}</div>
            <div>{{ page.props.orderItemStates }}</div>
        </div> -->

        <!-- View Mode -->
        <div class="flex flex-col gap-2" v-show="!showEdit">
            <div class="flex gap-x-3 border-b border-gray-100 items-center">
                <dt class="text-sm/6 font-medium text-gray-900">Title:</dt>
                <dd class="text-sm/6 text-gray-700">
                    {{ item.content?.title || 'N/A' }}
                </dd>
            </div>

            <div class="flex gap-x-3 border-b border-gray-100 items-center">
                <dt class="text-sm/6 font-medium text-gray-900">Content URL:</dt>
                <dd class="text-sm/6 text-gray-700">
                    <a v-if="item.content?.content_url" :href="item.content.content_url" target="_blank"
                        class="text-blue-500 hover:text-blue-700 flex items-center gap-1">
                        {{ item.content.content_url }}
                        <ExternalLink class="w-4 h-4" />
                    </a>
                    <span v-else>N/A</span>
                </dd>
            </div>

            <div class="flex gap-x-3 border-b border-gray-100 items-start">
                <dt class="text-sm/6 font-medium text-gray-900 pt-1">Content:</dt>
                <dd class="text-sm/6 text-gray-700 prose prose-sm max-w-none max-h-[200px] overflow-y-auto w-full">
                    <div v-if="item.content?.content_body" v-html="item.content.content_body"></div>
                    <span v-else>N/A</span>
                </dd>
            </div>

            <div class="flex gap-x-3 border-b border-gray-100 items-center">
                <dt class="text-sm/6 font-medium text-gray-900">Comments:</dt>
                <dd class="text-sm/6 text-gray-700 ">
                    {{ item.content?.comments || 'N/A' }}
                </dd>
            </div>


            <!-- View Mode - Media Files -->
            <div class="flex gap-x-3 border-b border-gray-100 items-center">
                <dt class="text-sm/6 font-medium text-gray-900">Media Files:</dt>
                <dd class="text-sm/6 text-gray-700">

                    <div v-if="media.length" class="flex flex-wrap gap-2">
                        <div v-for="media in media" :key="media.id"
                            class="flex items-center gap-2 bg-gray-50 px-3 py-1 rounded-lg">
                            <span class="text-sm text-gray-600">{{ media.name }}</span>
                            <a :href="media.url" target="_blank" class="text-blue-500 hover:text-blue-700">
                                <ExternalLink class="w-4 h-4" />
                            </a>
                        </div>
                    </div>
                    <span v-else>No files attached</span>
                </dd>
            </div>
        </div>

        <!-- Edit Mode -->
        <form @submit.prevent="updateContent"
            class="flex flex-col gap-3 bg-gray-50 border border-gray-200 rounded-lg p-6" v-show="showEdit">
            <div class="flex flex-row gap-2 items-center">
                <label for="title" class="w-40 text-sm/6 font-medium text-gray-900 whitespace-nowrap">Title:</label>
                <input type="text" id="title" class="w-full rounded-lg text-sm/6 text-gray-700"
                    v-model="editableContent.title" />
            </div>

            <div class="flex flex-row gap-2 items-center">
                <label for="content_url" class="w-40 text-sm/6 font-medium text-gray-900 whitespace-nowrap">Content
                    URL:</label>
                <input type="url" id="content_url" class="w-full rounded-lg text-sm/6 text-gray-700"
                    v-model="editableContent.content_url" placeholder="https://docs.google.com/..." />
            </div>

            <div class="flex flex-row gap-2 items-start">
                <label for="content_body"
                    class="w-40 text-sm/6 font-medium text-gray-900 whitespace-nowrap pt-2">Content:</label>
                <div class="w-full">
                    <RichTextEditor v-model="editableContent.content_body" placeholder="Start writing your content..."
                        :disabled="!canEditContent" />
                </div>
            </div>

            <div class="flex flex-row gap-2 items-center">
                <label for="comments"
                    class="w-40 text-sm/6 font-medium text-gray-900 whitespace-nowrap">Comments:</label>
                <textarea id="comments" class="w-full rounded-lg text-sm/6 text-gray-700"
                    v-model="editableContent.comments" rows="3"></textarea>
            </div>

            <!-- File Uploader -->
            <div class="flex flex-row gap-2 items-start">
                <label class="w-40 text-sm/6 font-medium text-gray-900 whitespace-nowrap pt-2">Media Files:</label>
                <div class="w-full">
                    <!-- Drop Zone -->
                    <div class="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center cursor-pointer hover:border-blue-500 transition-colors"
                        :class="{ 'border-blue-500 bg-blue-50': isDragging }" @dragover.prevent="isDragging = true"
                        @dragleave.prevent="isDragging = false" @drop.prevent="handleDrop" @click="triggerFileInput">
                        <input type="file" ref="fileInput" class="hidden" multiple
                            accept=".pdf,.doc,.docx,.png,.jpg,.jpeg" @change="handleFileSelect">
                        <div class="space-y-1 text-center">
                            <Upload class="mx-auto h-12 w-12 text-gray-400" />
                            <div class="flex text-sm text-gray-600">
                                <span>Drop files here or click to upload</span>
                            </div>
                            <p class="text-xs text-gray-500">
                                PDF, DOC, PNG, JPG up to 10MB
                            </p>
                        </div>
                    </div>

                    <!-- File List -->
                    <div v-if="selectedFiles.length > 0" class="mt-4 space-y-3">
                        <div v-for="(file, index) in selectedFiles" :key="index"
                            class="flex items-center justify-between bg-gray-50 px-3 py-3 rounded-lg border border-gray-200">
                            <div class="flex items-center gap-3">
                                <!-- Image Preview or File Icon -->
                                <div class="flex-shrink-0">
                                    <div v-if="isImageFile(file)" class="relative group">
                                        <img v-if="file.url" :src="file.url" :alt="file.name"
                                            class="w-12 h-12 object-cover rounded-lg shadow-sm border border-gray-200 cursor-pointer hover:shadow-md transition-shadow"
                                            @click="openImageModal(file)">
                                        <div v-else
                                            class="w-12 h-12 bg-gray-200 rounded-lg flex items-center justify-center">
                                            <svg class="w-6 h-6 text-gray-400" fill="none" stroke="currentColor"
                                                viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                    d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z">
                                                </path>
                                            </svg>
                                        </div>
                                    </div>
                                    <div v-else
                                        class="w-12 h-12 flex items-center justify-center bg-gray-100 rounded-lg border border-gray-200">
                                        <svg class="w-6 h-6 text-gray-400" fill="none" stroke="currentColor"
                                            viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z">
                                            </path>
                                        </svg>
                                    </div>
                                </div>

                                <div class="flex-1 min-w-0">
                                    <p class="text-sm font-medium text-gray-900 truncate">{{ file.name }}</p>
                                    <div class="flex items-center space-x-2 text-xs text-gray-500">
                                        <span>{{ formatFileSize(file.size) }}</span>
                                        <span v-if="isImageFile(file)"
                                            class="inline-flex items-center px-2 py-0.5 rounded-full bg-green-100 text-green-800 font-medium">
                                            Image
                                        </span>
                                    </div>
                                    <!-- Progress Bar -->
                                    <div v-if="file.uploading" class="mt-2">
                                        <div class="w-full bg-gray-200 rounded-full h-1.5">
                                            <div class="bg-blue-600 h-1.5 rounded-full transition-all duration-300"
                                                :style="{ width: file.progress + '%' }"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <button v-if="!file.uploading" type="button" @click="removeFile(index)"
                                class="text-gray-500 hover:text-red-500 p-1">
                                <X class="w-4 h-4" />
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <div class="flex flex-row gap-2 justify-end mt-2">
                <button type="button" class="btn-gray" @click="cancelEdit"
                    :disabled="isProcessingContent">Cancel</button>
                <button type="submit"
                    class="btn-indigo disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2"
                    :disabled="isProcessingContent">
                    <svg v-if="isProcessingContent" class="animate-spin h-4 w-4 text-white"
                        xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4">
                        </circle>
                        <path class="opacity-75" fill="currentColor"
                            d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z">
                        </path>
                    </svg>
                    {{ isProcessingContent ? 'Updating...' : 'Update Content' }}
                </button>
            </div>
        </form>

        <!-- Image Modal -->
        <div v-if="selectedImage" class="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 p-4"
            @click="closeImageModal">
            <div class="relative max-w-4xl max-h-full">
                <button @click="closeImageModal"
                    class="absolute -top-10 right-0 text-white hover:text-gray-300 transition-colors">
                    <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12">
                        </path>
                    </svg>
                </button>
                <img :src="selectedImage.url" :alt="selectedImage.name"
                    class="max-w-full max-h-full object-contain rounded-lg shadow-2xl" @click.stop>
                <div class="absolute bottom-0 left-0 right-0 bg-black bg-opacity-50 text-white p-4 rounded-b-lg">
                    <p class="text-sm font-medium">{{ selectedImage.name }}</p>
                    <p class="text-xs text-gray-300">{{ formatFileSize(selectedImage.size) }}</p>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, inject, computed } from 'vue'
import { Pencil, ExternalLink, Upload, X, AlertTriangle, Check } from 'lucide-vue-next'
import { router } from '@inertiajs/vue3'
import axios from 'axios'
import RichTextEditor from '@/Components/RichTextEditor.vue'


const page = inject("page");
const orderItemStates = page.props.orderItemStates;

const props = defineProps({
    item: Object,
    media: Object,
})

const media = computed(() => {
    return props.media.data
})

const notify = inject('$notify')

const showEdit = ref(false)
const isDragging = ref(false)
const fileInput = ref(null)
const selectedFiles = ref([])
const selectedImage = ref(null)

const emit = defineEmits(['content-updated'])

// Image modal functions
const openImageModal = (file) => {
    selectedImage.value = file
}

const closeImageModal = () => {
    selectedImage.value = null
}

// File type checking functions
const isImageFile = (file) => {
    if (file.url && (file.url.includes('.jpg') || file.url.includes('.jpeg') || file.url.includes('.png') || file.url.includes('.gif'))) {
        return true
    }
    return file.type && file.type.startsWith('image/')
}


// Local reactive editable copy
const editableContent = ref({
    title: '',
    content_body: '',
    content_url: '',
    comments: '',
})

// On Edit Click: Copy current data
const startEdit = () => {
    showEdit.value = true
    editableContent.value = {
        title: props.item.content?.title ?? '',
        content_body: props.item.content?.content_body ?? '',
        content_url: props.item.content?.content_url ?? '',
        comments: props.item.content?.comments ?? '',
    }
    // Load existing media files from the media prop
    if (media.value && Array.isArray(media.value)) {
        selectedFiles.value = media.value.map(media => ({
            name: media.name,
            url: media.url,
            size: media.size || 0,
            id: media.id
        }))
    }
}

// On Cancel
const cancelEdit = () => {
    showEdit.value = false
    selectedFiles.value = []
}

// File handling
const triggerFileInput = () => {
    fileInput.value.click()
}

const handleFileSelect = (event) => {
    const files = Array.from(event.target.files)
    validateAndAddFiles(files)
}

const handleDrop = (event) => {
    isDragging.value = false
    const files = Array.from(event.dataTransfer.files)
    validateAndAddFiles(files)
}

const validateAndAddFiles = async (files) => {
    const validTypes = ['.pdf', '.doc', '.docx', '.png', '.jpg', '.jpeg']
    const maxSize = 10 * 1024 * 1024 // 10MB

    for (const file of files) {
        const extension = '.' + file.name.split('.').pop().toLowerCase()
        if (!validTypes.includes(extension)) {
            notify(`Invalid file type: ${file.name}. Allowed types: PDF, DOC, PNG, JPG`, { type: 'error' })
            continue
        }
        if (file.size > maxSize) {
            notify(`File too large: ${file.name}. Maximum size is 10MB`, { type: 'error' })
            continue
        }

        // Add file to selected files with uploading state
        const fileIndex = selectedFiles.value.length
        selectedFiles.value.push({
            name: file.name,
            size: file.size,
            uploading: true,
            progress: 0
        })

        try {
            const formData = new FormData()
            formData.append('file', file)

            const response = await axios.post('/upload', formData, {
                headers: {
                    'Content-Type': 'multipart/form-data',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                },
                onUploadProgress: (progressEvent) => {
                    const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total)
                    selectedFiles.value[fileIndex].progress = progress
                }
            })

            if (response.data.success) {
                // Update file data with server response
                selectedFiles.value[fileIndex] = {
                    id: response.data.media_id,
                    name: response.data.name,
                    url: response.data.url,
                    size: response.data.size,
                    uploading: false,
                    progress: 100
                }
                notify('File uploaded successfully', { type: 'success' })
            }
        } catch (error) {
            // Remove failed upload from selected files
            selectedFiles.value.splice(fileIndex, 1)
            notify(`Failed to upload ${file.name}: ${error.response?.data?.message || 'Unknown error'}`, { type: 'error' })
        }
    }
}

const removeFile = (index) => {
    selectedFiles.value.splice(index, 1)
}

const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

const canEditContent = computed(() => {
    if (page.props?.auth?.user?.role === 'admin' || page.props?.auth?.user?.role === "superadmin") {
        return true;
    }
    const allowedStates = [
        page.props.orderItemStates.ContentPending,
        page.props.orderItemStates.ContentRevisionRequestedByAdvertiser,
        page.props.orderItemStates.ContentAssignedToWriter,
    ]

    return allowedStates.includes(props.item.state_name)
})

// Processing state for button protection
const isProcessingContent = ref(false)

// On Submit
const updateContent = () => {
    if (isProcessingContent.value) return; // Prevent multiple submissions

    isProcessingContent.value = true;

    // Create FormData to handle content update
    const formData = new FormData()

    // Add content data with media IDs
    const contentData = {
        ...editableContent.value,
        files_array: selectedFiles.value.map(file => file.id)
    }
    formData.append('content', JSON.stringify(contentData))

    // Update the original `item.content` object
    props.item.content = { ...editableContent.value }

    const routeName = page.props?.auth?.user?.role === 'writer'
        ? 'admin.writer.assignment.update-content'
        : 'admin.orders.item.update-content'

    router.post(route(routeName, props.item.id), formData, {
        preserveScroll: true,
        onSuccess: () => {
            notify("Content updated successfully!", { type: "success" })
            showEdit.value = false
            emit('content-updated')
            isProcessingContent.value = false
        },
        onError: (errors) => {
            // Handle validation errors
            Object.entries(errors).forEach(([field, message]) => {
                notify(`${message}`, { type: "error" });
            });
            isProcessingContent.value = false
        },
        onFinish: () => {
            isProcessingContent.value = false
        }
    });
}
</script>

<style lang="scss" scoped>
.btn-gray {
    @apply px-4 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2;
}

.btn-indigo {
    @apply px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2;
}
</style>