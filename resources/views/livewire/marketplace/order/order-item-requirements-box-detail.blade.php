@php
use App\Enums\OrderItemStates;
@endphp

<div class="bg-white border border-gray-200 rounded-lg shadow-sm" x-cloak>

    @if($item->state_name == OrderItemStates::PublicationRevisionRequestedByAdvertiser->value)
    <div class="p-4 border-b border-gray-100">
        <div class="mb-4 bg-amber-50 border border-amber-200 rounded-lg">
            <div class="p-3">
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <x-icons.etc.alert class="w-5 h-5 text-amber-500 mr-3 flex-shrink-0" />
                        <p class="text-amber-800 font-medium">{{ __('A new revision request is being processed.') }}</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    @endif

    @if($item->state_name == OrderItemStates::RequirementsPending->value || $item->state_name ==
    OrderItemStates::RequirementAwaitingPublisherApproval->value)
    <div class="p-4 border-b border-gray-100">
        <livewire:form.requirement-form :orderItem="$item"
            :disable_fields="$item->state_name == OrderItemStates::RequirementAwaitingPublisherApproval->value" />
    </div>

    @elseif($item->state_name == OrderItemStates::RequirementRevisionRequested->value)
    <div class="p-4 border-b border-gray-100">
        <div class="mb-4 bg-amber-50 border border-amber-200 rounded-lg">
            <div class="p-3">
                <div class="flex items-center">
                    <x-icons.etc.alert class="w-5 h-5 text-amber-500 mr-3 flex-shrink-0" />
                    <div>
                        <p class="text-amber-800 font-medium">Your requirements need revision. Please update them below.
                        </p>
                        <p class="mt-1 text-sm text-amber-700">
                            Reason: {{ $item->requirements->advertiser_revision_reason ?? 'No reason provided' }}
                        </p>
                    </div>
                </div>
            </div>
        </div>
        <livewire:form.requirement-form :orderItem="$item"
            :disable_fields="$item->state_name == OrderItemStates::RequirementAwaitingPublisherApproval->value" />
    </div>

    @elseif($item->state_name == OrderItemStates::ContentPending->value || $item->state_name ==
    OrderItemStates::ContentAwaitingPublisherApproval->value || $item->content != null && $item->state_name !=
    OrderItemStates::OrderItemCompleted->value)
    <div class="p-4 border-b border-gray-100">

        @if($item->is_content_provided_by_customer == 0 && ($item->state_name == OrderItemStates::ContentPending->value
        || $item->state_name == OrderItemStates::ContentAssignedToWriter->value))
        <div class="space-y-6">
            <div class="bg-white border border-gray-200 rounded-lg shadow-sm">
                <div class="p-6">
                    <div class="flex items-start">
                        <div class="flex-shrink-0">
                            <div class="p-2 bg-blue-50 rounded-lg">
                                <x-icons.lucide.badge-info class="w-6 h-6 text-blue-500" />
                            </div>
                        </div>
                        <div class="ml-4">
                            <h3 class="text-lg font-semibold text-gray-900">Content is being prepared</h3>
                            <p class="mt-2 text-gray-600">
                                Our team is working on your content. You will receive an email notification as soon as
                                it's ready.
                            </p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="flex justify-center bg-gray-50 rounded-lg p-8">
                <dotlottie-player src="https://lottie.host/37ab75c4-4860-4680-852f-3ab51b249783/04RkV14knl.lottie"
                    background="transparent" speed="1" style="width: 280px; height: 280px" loop autoplay>
                </dotlottie-player>
            </div>
        </div>
        <script src="https://unpkg.com/@dotlottie/player-component@2.7.12/dist/dotlottie-player.mjs" type="module">
        </script>
        @else
        @if($item->state_name == OrderItemStates::ContentAdvertiserReview->value)
        <div class="mb-4 bg-blue-50 border border-blue-200 rounded-lg">
            <div class="p-4">
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <x-icons.lucide.badge-info class="w-5 h-5 text-blue-500 mr-3 flex-shrink-0" />
                        <p class="text-blue-800 font-medium">Please review the content and provide your feedback.</p>
                    </div>
                    <div class="flex items-center space-x-3">
                        <button wire:click="approveContent" wire:loading.attr="disabled" wire:target="approveContent"
                            class="inline-flex items-center px-4 py-2 bg-green-600 text-white rounded-lg shadow-sm hover:bg-green-700 hover:shadow-md transition-all duration-200 font-medium disabled:opacity-50 disabled:cursor-not-allowed">
                            <span wire:loading.remove wire:target="approveContent" class="inline-flex items-center">
                                <x-icons.lucide.check class="w-4 h-4 mr-2" />
                                Approve Content
                            </span>
                            <span wire:loading wire:target="approveContent" class="inline-flex items-center">
                                <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white"
                                    xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor"
                                        stroke-width="4"></circle>
                                    <path class="opacity-75" fill="currentColor"
                                        d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z">
                                    </path>
                                </svg>
                                Approving...
                            </span>
                        </button>
                        <button wire:click="$set('showDisapproveModal', true)"
                            class="inline-flex items-center px-4 py-2 bg-red-600 text-white rounded-lg shadow-sm hover:bg-red-700 hover:shadow-md transition-all duration-200 font-medium">
                            <x-icons.lucide.close-x class="w-4 h-4 mr-2" />
                            Request Changes
                        </button>
                    </div>
                </div>
            </div>

        </div>
        @elseif($item->state_name == OrderItemStates::ContentAwaitingPublisherApproval->value)
        <div class="mb-4 bg-amber-50 border border-amber-200 rounded-lg">
            <div class="p-4">
                <div class="flex items-center">
                    <x-icons.etc.alert class="w-5 h-5 text-amber-500 mr-3 flex-shrink-0" />
                    <p class="text-amber-800 font-medium">Content is currently under review by the publisher.</p>
                </div>
            </div>
        </div>
        @endif
        @php
        $revisionReason = $item->content->publisher_advertiser_revision_reason ?? null;
        @endphp
        @if($item->state_name == OrderItemStates::ContentPending->value && $revisionReason != null)
        <div class="mb-4 bg-amber-50 border border-amber-200 rounded-lg">
            <div class="p-4">
                <div class="flex items-start">
                    <div class="flex-shrink-0">
                        <div class="p-2 bg-amber-100 rounded-lg">
                            <x-icons.etc.alert class="w-5 h-5 text-amber-600" />
                        </div>
                    </div>
                    <div class="ml-4">
                        <h3 class="text-lg font-semibold text-amber-800">Content Needs Revision</h3>
                        <div class="mt-2 bg-white rounded-lg border border-amber-200 p-3">
                            <div class="flex items-start">
                                <x-icons.lucide.message-square
                                    class="w-5 h-5 text-amber-500 mr-3 flex-shrink-0 mt-0.5" />
                                <div>
                                    <p class="text-sm font-medium text-amber-800">Publisher's Feedback:</p>
                                    <p class="mt-1 text-sm text-amber-700">
                                        {{ $item->content->publisher_advertiser_revision_reason ?? 'No feedback
                                        provided' }}
                                    </p>
                                </div>
                            </div>
                        </div>
                        <div class="mt-2 flex items-center text-sm text-amber-600">
                            <x-icons.lucide.badge-info class="w-4 h-4 mr-2 flex-shrink-0" />
                            <p>Please review the feedback and make the necessary changes to your content. Request has
                                been sent to the writer.</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        @elseif($item->state_name == OrderItemStates::PublicationInProcess->value)
        <div class="mb-4 bg-blue-50 border border-blue-200 rounded-lg">
            <div class="p-4">
                <div class="flex items-center">
                    <x-icons.lucide.badge-info class="w-5 h-5 text-blue-500 mr-3 flex-shrink-0" />
                    <p class="text-blue-800 font-medium">Content is currently being published.</p>
                </div>
            </div>
        </div>
        @elseif($item->state_name == OrderItemStates::PublicationDelivered->value)
        <div
            class="mb-4 bg-green-50 border border-green-200 rounded-lg transform transition-all duration-300 hover:shadow-md">
            <div class="p-4">
                <div class="flex items-center">
                    <div class="flex-shrink-0 p-2 bg-green-100 rounded-full">
                        <x-icons.lucide.badge-info class="w-5 h-5 text-green-600" />
                    </div>
                    <div class="ml-3 flex-1">
                        <p class="text-green-800 font-medium text-lg">Content has been published successfully!</p>
                        <div class="mt-3 flex flex-wrap gap-3 items-center">
                            <a href="{{ $item->publication->publication_url ?? '#' }}" target="_blank"
                                class="inline-flex items-center text-green-700 hover:text-green-900 font-medium transition-colors duration-200">
                                <x-icons.lucide.external-link class="w-4 h-4 mr-2" />
                                View Published URL
                                <span class="ml-1 text-sm text-green-600">(opens in new tab)</span>
                            </a>
                            <div class="flex gap-2">
                                <button wire:click="approvePublication" wire:loading.attr="disabled"
                                    wire:target="approveContent"
                                    class="inline-flex items-center px-3 py-1.5 bg-green-600 text-white text-sm rounded-md shadow-sm hover:bg-green-700 hover:shadow-md transition-all duration-200 font-medium disabled:opacity-50 disabled:cursor-not-allowed">
                                    <span wire:loading.remove wire:target="approveContent"
                                        class="inline-flex items-center">
                                        <x-icons.lucide.check class="w-4 h-4 mr-1.5" />
                                        Approve
                                    </span>
                                    <span wire:loading wire:target="approveContent" class="inline-flex items-center">
                                        <svg class="animate-spin -ml-1 mr-1.5 h-4 w-4 text-white"
                                            xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor"
                                                stroke-width="4"></circle>
                                            <path class="opacity-75" fill="currentColor"
                                                d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z">
                                            </path>
                                        </svg>
                                        Approving...
                                    </span>
                                </button>
                                <button wire:click="$set('showRevisionModal', true)"
                                    class="inline-flex items-center px-3 py-1.5 bg-amber-500 text-white text-sm rounded-md shadow-sm hover:bg-amber-600 hover:shadow-md transition-all duration-200 font-medium">
                                    <x-icons.lucide.close-x class="w-4 h-4 mr-1.5" />
                                    Request Revision
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        @endif

        <livewire:form.content-form :orderItem="$item" />

        @endif
    </div>
    @endif

    @if($item->state_name == OrderItemStates::RequirementAwaitingPublisherApproval->value)
    <div class="p-4 border-b border-gray-100">
        <div class="mb-4 bg-amber-50 border border-amber-200 rounded-lg">
            <div class="p-3">
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <x-icons.etc.alert class="w-5 h-5 text-amber-500 mr-3 flex-shrink-0" />
                        <p class="text-amber-800 font-medium">Your requirement is awaiting publisher approval.</p>
                    </div>
                    <div class="flex items-center space-x-4">
                        @if($showResendButton)
                        <button wire:click="resendEmail"
                            class="text-amber-800 hover:text-amber-900 font-medium flex items-center">
                            <x-icons.lucide.mail class="w-4 h-4 mr-1" />
                            Resend Reminder
                        </button>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
    @endif

    {{-- Chat Modal --}}
    @if($showChat)
    <div class="fixed z-[10] inset-0 bg-gray-500 bg-opacity-75 transition-opacity" wire:click="closeChat"></div>
    <div class="fixed inset-0 z-10 overflow-y-auto">
        <div class="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0">
            <div
                class="relative transform overflow-hidden rounded-lg bg-white px-4 pb-4 pt-5 text-left shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-lg sm:p-6">
                <div class="absolute right-0 top-0 pr-4 pt-4">
                    <button type="button" wire:click="closeChat"
                        class="rounded-md bg-white text-gray-400 hover:text-gray-500 focus:outline-none">
                        <span class="sr-only">Close</span>
                        <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>
                <livewire:marketplace.order.order-item-chat :item="$item" />
            </div>
        </div>
    </div>
    @endif



    {{-- Bottom bar with published URL --}}
    @if(isset($item->publication->publication_url) && !empty($item->publication->publication_url))
    <div class="p-4 bg-gray-50 border-t border-gray-200">
        <div class="flex flex-col sm:flex-row items-center justify-between">
            <div class="flex items-center mb-4 sm:mb-0">
                <x-icons.lucide.newspaper class="w-5 h-5 text-gray-500 mr-3" />
                <span class="font-medium text-gray-700">Published URL:</span>
            </div>
            <div class="flex items-center">
                <a target="_blank" href="{{ $item->publication->publication_url ?? '#' }}"
                    class="text-blue-600 hover:text-blue-800 flex items-center group">
                    <span class="italic underline mr-2">{{ $item->publication->publication_url ?? 'N/A' }}</span>
                    <x-icons.lucide.external-link class="w-4 h-4 group-hover:translate-x-0.5 transition-transform" />
                </a>
                <span class="ml-4 text-sm text-gray-500">{{ $item->delivery_date ?? 'N/A' }}</span>
            </div>
        </div>
    </div>
    @endif



    {{-- Disapprove Content Modal --}}
    <div x-show="$wire.showDisapproveModal" class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity"
        wire:click="$set('showDisapproveModal', false)"></div>
    <div x-show="$wire.showDisapproveModal" class="fixed inset-0 z-10 overflow-y-auto">
        <div class="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0">
            <div
                class="relative transform overflow-hidden rounded-lg bg-white px-4 pb-4 pt-5 text-left shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-lg sm:p-6">
                <div class="absolute right-0 top-0 pr-4 pt-4">
                    <button type="button" wire:click="$set('showDisapproveModal', false)"
                        class="rounded-md bg-white text-gray-400 hover:text-gray-500 focus:outline-none">
                        <span class="sr-only">Close</span>
                        <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>
                <div class="sm:flex sm:items-start">
                    <div
                        class="mx-auto flex h-12 w-12 flex-shrink-0 items-center justify-center rounded-full bg-red-100 sm:mx-0 sm:h-10 sm:w-10">
                        <x-icons.lucide.close-x class="h-6 w-6 text-red-600" />
                    </div>
                    <div class="mt-3 text-center sm:ml-4 sm:mt-0 sm:text-left">
                        <h3 class="text-base font-semibold leading-6 text-gray-900">Request Content Changes</h3>
                        <div class="mt-2">
                            <p class="text-sm text-gray-500">Please provide details about what changes are needed for
                                the content.</p>
                        </div>
                    </div>
                </div>
                <div class="mt-4">
                    <textarea wire:model="revisionReason" rows="4"
                        class="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-red-600 sm:text-sm sm:leading-6"
                        placeholder="Enter revision reason..."></textarea>
                </div>
                <div class="mt-5 sm:mt-4 sm:flex sm:flex-row-reverse">
                    <button type="button" wire:click="disapproveContent" wire:loading.attr="disabled"
                        wire:target="disapproveContent"
                        class="inline-flex w-full justify-center rounded-md bg-red-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-red-500 sm:ml-3 sm:w-auto disabled:opacity-50 disabled:cursor-not-allowed">
                        <span wire:loading.remove wire:target="disapproveContent">Submit Changes Request</span>
                        <span wire:loading wire:target="disapproveContent" class="flex items-center">
                            <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg"
                                fill="none" viewBox="0 0 24 24">
                                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor"
                                    stroke-width="4"></circle>
                                <path class="opacity-75" fill="currentColor"
                                    d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z">
                                </path>
                            </svg>
                            Submitting...
                        </span>
                    </button>
                    <button type="button" wire:click="$set('showDisapproveModal', false)"
                        class="mt-3 inline-flex w-full justify-center rounded-md bg-white px-3 py-2 text-sm font-semibold text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 hover:bg-gray-50 sm:mt-0 sm:w-auto">Cancel</button>
                </div>
            </div>
        </div>
    </div>

    {{-- Revision Request Modal --}}
    <div x-show="$wire.showRevisionModal" class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity"
        wire:click="$set('showRevisionModal', false)"></div>
    <div x-show="$wire.showRevisionModal" class="fixed inset-0 z-10 overflow-y-auto">
        <div class="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0">
            <div
                class="relative transform overflow-hidden rounded-lg bg-white px-4 pb-4 pt-5 text-left shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-lg sm:p-6">
                <div class="absolute right-0 top-0 pr-4 pt-4">
                    <button type="button" wire:click="$set('showRevisionModal', false)"
                        class="rounded-md bg-white text-gray-400 hover:text-gray-500 focus:outline-none">
                        <span class="sr-only">Close</span>
                        <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>
                <div class="sm:flex sm:items-start">
                    <div
                        class="mx-auto flex h-12 w-12 flex-shrink-0 items-center justify-center rounded-full bg-amber-100 sm:mx-0 sm:h-10 sm:w-10">
                        <x-icons.lucide.close-x class="h-6 w-6 text-amber-600" />
                    </div>
                    <div class="mt-3 text-center sm:ml-4 sm:mt-0 sm:text-left">
                        <h3 class="text-base font-semibold leading-6 text-gray-900">Request Publication Revision</h3>
                        <div class="mt-2">
                            <p class="text-sm text-gray-500">Please provide specific details about what changes are
                                needed for the content.</p>
                        </div>
                    </div>
                </div>
                <div class="mt-4">
                    <div class="space-y-4">
                        <div>
                            <label for="revisionType" class="block text-sm font-medium text-gray-700">Revision
                                Type</label>
                            <select wire:model="revisionType" id="revisionType"
                                class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-amber-500 focus:ring-amber-500 sm:text-sm">
                                <option value="">Select a type</option>
                                <option value="content">Content Changes</option>
                                <option value="formatting">Formatting Changes</option>
                                <option value="technical">Technical Issues</option>
                                <option value="other">Other</option>
                            </select>
                        </div>
                        <div>
                            <label for="revisionReason" class="block text-sm font-medium text-gray-700">Revision
                                Details</label>
                            <textarea wire:model="revisionReason" id="revisionReason" rows="4"
                                class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-amber-500 focus:ring-amber-500 sm:text-sm"
                                placeholder="Please provide specific details about the required changes..."></textarea>
                        </div>
                    </div>
                </div>
                <div class="mt-5 sm:mt-4 sm:flex sm:flex-row-reverse">
                    <button type="button" wire:click="submitRevisionRequest" wire:loading.attr="disabled"
                        wire:target="submitRevisionRequest"
                        class="inline-flex w-full justify-center rounded-md bg-amber-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-amber-500 sm:ml-3 sm:w-auto disabled:opacity-50 disabled:cursor-not-allowed">
                        <span wire:loading.remove wire:target="submitRevisionRequest" class="inline-flex items-center">
                            <x-icons.lucide.send class="w-4 h-4 mr-2" />
                            Submit Revision Request
                        </span>
                        <span wire:loading wire:target="submitRevisionRequest" class="inline-flex items-center">
                            <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg"
                                fill="none" viewBox="0 0 24 24">
                                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor"
                                    stroke-width="4"></circle>
                                <path class="opacity-75" fill="currentColor"
                                    d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z">
                                </path>
                            </svg>
                            Submitting...
                        </span>
                    </button>
                    <button type="button" wire:click="$set('showRevisionModal', false)"
                        class="mt-3 inline-flex w-full justify-center rounded-md bg-white px-3 py-2 text-sm font-semibold text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 hover:bg-gray-50 sm:mt-0 sm:w-auto">Cancel</button>
                </div>
            </div>
        </div>
    </div>


    @if($item->state != OrderItemStates::RequirementsPending->value)
    <div class="fixed bottom-6 right-6 z-50">
        <button wire:click="openChat"
            class="inline-flex items-center justify-center w-14 h-14 bg-amber-500 text-white rounded-full shadow-lg hover:bg-amber-600 hover:shadow-xl transition-all duration-200 transform hover:scale-110"
            title="Chat with Publisher">
            <x-icons.lucide.chat class="w-6 h-6" />
        </button>
    </div>
    @endif

</div>