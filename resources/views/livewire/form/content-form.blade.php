<div class="bg-white rounded-lg shadow-sm p-2 sm:p-8"
    x-data="{ content_source: @entangle('content_source'), orderItem: @entangle('orderItem') }">
    <form wire:submit="submit" class="space-y-8">
        {{-- Content Source Section --}}
        <div class="bg-gray-50 p-6 rounded-lg" x-show="content_source == 1">
            <div class="space-y-4">
                <div class="flex items-center justify-between">
                    <label class="text-lg font-medium text-gray-900 flex items-center gap-2">
                        <x-icons.lucide.pen-line class="w-5 h-5 text-emerald-600" />
                        Content Source
                    </label>
                    <x-ui.tooltip text="?"
                        tooltip="Choose whether you want to provide content or have our writers create it" />
                </div>
                <div class="space-y-3">
                    <div class="flex items-center space-x-3" x-show="">
                        <input @disabled($disable_fields) type="radio" id="provide_own_content" name="content_source"
                            wire:model.live="content_source" value="1"
                            class="h-5 w-5 border-gray-300 text-emerald-600 focus:ring-emerald-600">
                        <label for="provide_own_content" class="text-gray-700 font-medium">
                            I will provide my own content
                        </label>
                    </div>
                </div>
            </div>
        </div>


        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8" x-show="">
            {{-- Left Column --}}
            <div class="space-y-6">
                {{-- Title --}}
                <div class="space-y-2">
                    <div class="flex items-center justify-between">
                        <label for="title" class="text-lg font-medium text-gray-900 flex items-center gap-2">
                            <x-icons.lucide.type-outline class="w-5 h-5 text-emerald-600" />
                            Article Title
                            <span class="text-red-500">*</span>
                        </label>
                        <div class="flex items-center gap-2">

                            <x-ui.tooltip text="?" tooltip="Enter the title of your article" />
                        </div>
                    </div>
                    <input @disabled($disable_fields) type="text" id="title" wire:model.defer="title"
                        class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 @error('title') border-red-500 @enderror"
                        placeholder="Enter your article title">
                    @error('title') <p class="text-red-500 text-sm mt-1">{{ $message }}</p> @enderror
                </div>

                {{-- Content URL --}}
                <div class="space-y-2">
                    <div class="flex items-center justify-between">
                        <label for="content_url" class="text-lg font-medium text-gray-900 flex items-center gap-2">
                            <x-icons.lucide.link class="w-5 h-5 text-emerald-600" />
                            Content URL
                        </label>
                        <x-ui.tooltip text="?" tooltip="Link to your content (Google Docs, Notion, etc.)" />
                    </div>
                    <input @disabled($disable_fields) type="url" id="content_url" wire:model.defer="content_url"
                        class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 @error('content_url') border-red-500 @enderror"
                        placeholder="https://docs.google.com/...">
                    @error('content_url') <p class="text-red-500 text-sm mt-1">{{ $message }}</p> @enderror
                </div>

                {{-- File Upload --}}
                <div class="space-y-2" wire:ignore>
                    <div class="flex items-center justify-between">
                        <label for="files" class="text-lg font-medium text-gray-900 flex items-center gap-2">
                            <x-icons.lucide.globe-earth class="w-5 h-5 text-emerald-600" />
                            Upload Files
                        </label>
                        <x-ui.tooltip text="?" tooltip="Upload supporting files (images, documents, etc.)" />
                    </div>
                    @if (!$disable_fields)
                    <div class="dropzone" id="myDropzone">
                        <div class="dz-message" data-dz-message>
                            <div class="space-y-1 text-center">
                                <x-icons.lucide.globe-earth class="mx-auto h-12 w-12 text-gray-400" />
                                <div class="flex text-sm text-gray-600">
                                    <span>Drop files here or click to upload</span>
                                </div>
                                <p class="text-xs text-gray-500">
                                    PNG, JPG, PDF up to 10MB
                                </p>
                            </div>
                        </div>
                    </div>
                    @endif
                    @if($orderItem->content?->media->count() > 0)
                    <div class="space-y-4">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            @foreach($orderItem->content->media as $media)
                            <div
                                class="bg-gray-50 border border-gray-200 rounded-lg p-4 hover:border-emerald-500 transition-colors duration-200">
                                <div class="flex items-center space-x-4">
                                    <div class="flex-shrink-0">
                                        @if(str_contains($media->mime_type, 'image'))
                                        <img src="{{ $media->url }}" alt="Uploaded image"
                                            class="w-16 h-16 object-cover rounded-lg shadow-sm">
                                        @else
                                        <div class="w-16 h-16 flex items-center justify-center bg-gray-100 rounded-lg">
                                            <x-icons.lucide.globe-earth class="w-8 h-8 text-gray-400" />
                                        </div>
                                        @endif
                                    </div>
                                    <div class="flex-1 min-w-0">
                                        <p class="text-sm font-medium text-gray-900 truncate mb-1">
                                            {{ $media->meta['name'] }}
                                        </p>
                                        <div class="flex items-center space-x-2 text-xs text-gray-500">
                                            <span>{{ number_format($media->meta['size'] / 1024, 2) }} KB</span>
                                            <span>•</span>
                                            <span>{{ Str::upper(pathinfo($media->meta['name'], PATHINFO_EXTENSION))
                                                }}</span>
                                        </div>
                                    </div>
                                    <div class="flex-shrink-0">
                                        <a href="{{ $media->url }}" target="_blank" rel="noopener noreferrer"
                                            class="inline-flex items-center px-2.5 py-1.5 text-xs font-medium text-emerald-700 bg-emerald-50 hover:bg-emerald-100 rounded-md transition-colors duration-200">
                                            <x-icons.lucide.external-link class="w-4 h-4 mr-1" />
                                            View
                                        </a>
                                    </div>
                                </div>
                            </div>
                            @endforeach
                        </div>
                    </div>
                    @else
                    <div class="bg-gray-50 border border-gray-200 rounded-lg p-4">
                        <p class="text-sm text-gray-600">No files uploaded</p>
                    </div>
                    @endif
                    <input @disabled($disable_fields) type="hidden" name="media_ids" id="media_ids"
                        wire:model="media_ids">
                    @error('files') <p class="text-red-500 text-sm mt-1">{{ $message }}</p> @enderror
                </div>
            </div>

            {{-- Right Column --}}
            <div class="space-y-6">
                {{-- Content Body --}}
                <div class="space-y-2">
                    <div class="flex items-center justify-between">
                        <label for="content_body" class="text-lg font-medium text-gray-900 flex items-center gap-2">
                            <x-icons.lucide.mail class="w-5 h-5 text-emerald-600" />
                            Content
                            <span class="text-red-500">*</span>
                        </label>
                        <div class="flex items-center gap-2">

                            <x-ui.tooltip text="?" tooltip="Paste your article content here" />
                        </div>
                    </div>
                    <x-rich-text-editor wire:model.defer="content_body" model="content_body" :disabled="$disable_fields"
                        id="content_body" placeholder="Paste your article content here..."
                        class="w-full @error('content_body') border-red-500 @enderror" />
                    @error('content_body') <p class="text-red-500 text-sm mt-1">{{ $message }}</p> @enderror
                </div>
            </div>
        </div>

        {{-- Comments Section --}}
        <div class="space-y-2" x-show="">
            <div class="flex items-center justify-between">
                <label for="comments" class="text-lg font-medium text-gray-900 flex items-center gap-2">
                    <x-icons.lucide.mail class="w-5 h-5 text-emerald-600" />
                    Additional Comments
                </label>
                <x-ui.tooltip text="?" tooltip="Any additional notes or instructions" />
            </div>
            <textarea @disabled($disable_fields) id="comments" wire:model.defer="comments" rows="5"
                class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 @error('comments') border-red-500 @enderror"
                placeholder="Any additional notes or instructions..."></textarea>
            @error('comments') <p class="text-red-500 text-sm mt-1">{{ $message }}</p> @enderror
        </div>


        {{-- Submit Button --}}
        @if (!$disable_fields)
        <div class="flex justify-end mt-2">
            <button wire:loading.attr="disabled" wire:target="submit" type="submit"
                class="py-2 px-6 bg-emerald-700 text-white font-semibold rounded-md hover:bg-emerald-800">
                @if($content_source == 0)
                Write Content
                @elseif ($orderItem->content)
                Update Content
                @else
                Submit Content
                @endif
            </button>
        </div>
        @endif
    </form>
</div>