<?php

namespace App\Services;

use App\Models\MarketplaceSingleOrderItem;
use App\Models\User;
use Illuminate\Notifications\DatabaseNotification;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;

/**
 * Notification Service
 *
 * Handles notification processing and formatting for Inertia.js frontend.
 * Encapsulates notification logic that was previously in AppServiceProvider.
 *
 * Responsibilities:
 * - Fetch and format user notifications
 * - Generate appropriate navigation links based on user role
 * - Handle order item data loading for notifications
 */
class NotificationService
{
    /**
     * Get formatted notifications for the authenticated user
     *
     * @return array Array of formatted notifications
     */
    public function getFormattedNotifications(): array
    {
        $user = Auth::user();

        // Return empty array if no authenticated user
        if (! $user) {
            return [];
        }

        // Fetch user notifications
        $notifications = $user->notifications()
            ->latest()
            ->get();

        // Extract order item IDs from notifications
        $orderItemIds = collect($notifications)
            ->pluck('data.order_id')
            ->filter()
            ->unique();

        // Load order items data if needed
        $orderItems = $this->loadOrderItems($orderItemIds);

        // Format notifications with appropriate links
        return $notifications->map(fn($notification) => $this->formatNotification($notification, $orderItems))->toArray();
    }

    /**
     * Load order items data for notifications
     *
     * @param Collection $orderItemIds Collection of order item IDs
     * @return Collection Collection of order items keyed by ID
     */
    private function loadOrderItems($orderItemIds)
    {
        if ($orderItemIds->isEmpty()) {
            return collect();
        }

        return MarketplaceSingleOrderItem::with('order')
            ->whereIn('id', $orderItemIds)
            ->without(['website', 'requirements', 'content'])
            ->get()
            ->keyBy('id');
    }

    /**
     * Format a single notification with appropriate navigation link
     *
     * @param DatabaseNotification $notification The notification to format
     * @param Collection $orderItems Collection of order items keyed by ID
     * @return array Formatted notification data
     */
    private function formatNotification($notification, $orderItems): array
    {
        $data = $notification->data;
        $orderId = $data['order_id'] ?? null;
        $href = '#';

        // Generate appropriate navigation link based on user role and order data
        if ($orderId) {
            $href = $this->generateNotificationLink($orderId, $orderItems);
        }

        return [
            'id' => $notification->id,
            'message' => $data['message'] ?? '',
            'href' => $href,
            'icon' => 'MessageSquare',
            'read_at' => $notification->read_at,
            'created_at' => $notification->created_at,
        ];
    }

    /**
     * Generate navigation link for notification based on user role
     *
     * @param int $orderId The order item ID
     * @param Collection $orderItems Collection of order items keyed by ID
     * @return string The generated navigation link
     */
    public function generateNotificationLink($orderId, $orderItems, $user = null): string
    {

        // Return default route if no authenticated user
        if (!$user) {
            $user = Auth::user();
        }

        // Admin/SuperAdmin users get admin-specific links
//        if ($user->isSuperAdmin() || $user->isAdmin()) {
//            $orderItem = $orderItems->get($orderId);
//            if ($orderItem && $orderItem->order) {
//                return route('admin.orders.item', [
//                    'order' => $orderItem->order->id,
//                    'item' => $orderItem->id,
//                ]);
//            }
//        } elseif ($user->isWriter()) {
//            $orderItem = $orderItems->get($orderId);
//            if ($orderItem && $orderItem->order) {
//                return route('admin.writer.assignment.show', $orderItem->id);
//            }
//        }

        // Other users get publisher-specific links
        return route('publisher.orders.details', $orderId);
    }
}
