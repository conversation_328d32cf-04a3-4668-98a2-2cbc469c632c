<?php

namespace App\Livewire\Advertiser;

use Domain\Wallet\Wallet;
use App\Models\WalletWithdrawalRequest;
use Illuminate\Support\Facades\Auth;
use Livewire\Component;

class WalletWithdraw extends Component
{
    public $amount = '';

    public $selectedRequest = null;

    private Wallet $wallet;

    public function __construct()
    {
        $this->wallet = new Wallet();
    }


    public function rules(): array
    {
        $user = Auth::user();
        $balance = $user->formatted_balance;

        return [
            'amount' => 'required|numeric|max:' . $balance,
        ];
    }

    public function mount(): void {}

    public function getBalanceProperty()
    {
        return Auth::user()->user_balance;
    }

    public function getWithdrawalRequestsProperty()
    {
        $user = Auth::user();
        if ($user) {
            return WalletWithdrawalRequest::where('user_id', $user->id)
                ->with('paymentMethod')
                ->orderBy('id', 'desc')
                ->get();
        }
    }

    public function withdraw()
    {
        $this->validate();

        $result = $this->wallet->withdraw(Auth::user(), [
            'amount' => $this->amount,
        ]);

        if (!$result) {
            $this->js("toast('Failed to withdraw', {type: 'error', position: 'bottom-center'})");
        }

        $this->js("toast('Withdrawal request submitted successfully', {type: 'success', position: 'bottom-center'})");
        $this->amount = '';
    }

    public function showRequestDetails($requestId): void
    {
        $this->selectedRequest = WalletWithdrawalRequest::with(['paymentMethod', 'media'])
            ->find($requestId);
    }

    public function closeRequestDetails(): void
    {
        $this->selectedRequest = null;
    }

    public function render()
    {
        return view('livewire.advertiser.wallet-withdraw');
    }
}
